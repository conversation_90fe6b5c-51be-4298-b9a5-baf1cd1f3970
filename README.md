# Registration Backend API

A secure user authentication backend built with FastAPI, SQLite, JWT tokens, and comprehensive session management.

## Features

### Core Authentication
- **User Registration**: Secure user registration with email validation and password strength requirements
- **User Authentication**: Hybrid JWT + Session-based authentication system
- **Password Security**: Bcrypt password hashing with configurable strength
- **Input Validation**: Comprehensive validation using Pydantic schemas

### Session Management 🆕
- **Persistent Sessions**: Database-stored sessions with configurable expiration
- **Multi-Device Support**: Track and manage sessions across multiple devices
- **Device Fingerprinting**: Capture and store device/browser information for security
- **Session Control**: Terminate individual sessions or all sessions at once
- **Automatic Cleanup**: Expired sessions are automatically removed

### Security & Infrastructure
- **API Documentation**: Automatic OpenAPI/Swagger documentation
- **Database**: SQLite with SQLAlchemy ORM (PostgreSQL ready)
- **CORS Support**: Configurable cross-origin resource sharing
- **UUID Primary Keys**: Secure, unpredictable identifiers
- **SQL Injection Protection**: SQLAlchemy ORM prevents SQL injection attacks

## Technology Stack

- **Framework**: FastAPI (Python web framework)
- **Database**: SQLite with SQLAlchemy ORM (PostgreSQL ready)
- **Authentication**: Hybrid JWT + Session-based authentication
- **Session Storage**: Database-persistent sessions with UUID identifiers
- **Password Hashing**: bcrypt via passlib
- **Validation**: Pydantic schemas with comprehensive validation rules
- **Server**: Uvicorn ASGI server with hot reload support

## Prerequisites

Before running this application, ensure you have the following installed on your system:

### System Requirements

- **Python 3.8 or higher** (Tested with Python 3.12.5)
  - Check your Python version: `python3 --version`
  - Download from: https://www.python.org/downloads/

- **pip** (Python package installer)
  - Usually comes with Python installation
  - Check if installed: `pip --version`

### Optional but Recommended

- **Git** (for version control)
  - Download from: https://git-scm.com/downloads
  - Check if installed: `git --version`

- **Virtual Environment Tools**
  - `venv` (included with Python 3.3+)
  - Or `virtualenv`: `pip install virtualenv`

### Operating System Support

This application has been tested on:
- **macOS** (Primary development environment)
- **Linux** (Ubuntu, CentOS, etc.)
- **Windows** (with Python 3.8+)

## Project Structure

```text
registration_backend/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI application entry point
│   ├── config.py            # Configuration settings (includes session config)
│   ├── database.py          # Database connection and setup
│   ├── models/
│   │   ├── __init__.py
│   │   ├── user.py          # User SQLAlchemy model
│   │   └── session.py       # Session SQLAlchemy model 🆕
│   ├── schemas/
│   │   ├── __init__.py
│   │   ├── user.py          # User Pydantic schemas
│   │   └── session.py       # Session Pydantic schemas 🆕
│   ├── routes/
│   │   ├── __init__.py
│   │   ├── auth.py          # Authentication routes (enhanced)
│   │   └── session.py       # Session management routes 🆕
│   └── utils/
│       ├── __init__.py
│       ├── auth.py          # JWT and password utilities (enhanced)
│       ├── session.py       # Session management utilities 🆕
│       └── dependencies.py  # FastAPI dependencies (enhanced)
├── frontend_session_integration/  # Frontend integration files 🆕
├── venv/                    # Virtual environment
├── requirements.txt         # Python dependencies (updated)
├── .env                     # Environment variables
├── .env.template           # Environment template (updated)
├── SESSION_MANAGEMENT_DOCS.md  # Session system documentation 🆕
├── migrate_add_sessions.py # Database migration tool 🆕
├── test_session_functionality.py  # Session tests 🆕
├── .gitignore              # Git ignore rules
├── run.py                  # Application runner
└── README.md               # This file
```

## Quick Start

> **Note**: Make sure you have met all the [Prerequisites](#prerequisites) before proceeding.

### 1. Setup Environment

```bash
# Clone or navigate to the project directory
cd registration_backend

# Create virtual environment (if not already created)
python3 -m venv venv

# Activate virtual environment
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration

Copy the environment template and modify as needed:

```bash
cp .env.template .env
```

Edit `.env` file with your configuration:

- `SECRET_KEY`: Change to a secure random key for production
- `DATABASE_URL`: SQLite database path (default: `sqlite:///./registration.db`)
- `ACCESS_TOKEN_EXPIRE_MINUTES`: JWT token expiration time (default: 30 minutes)
- `SESSION_EXPIRE_HOURS`: Session expiration time (default: 168 hours / 7 days)
- `SESSION_CLEANUP_INTERVAL_HOURS`: Automatic cleanup interval (default: 24 hours)

### 3. Run the Application

```bash
# Using the runner script
python run.py

# Or directly with uvicorn
uvicorn app.main:app --reload --host 127.0.0.1 --port 8080
```

The application will start on `http://localhost:8080`

### 4. Access API Documentation

- **Swagger UI**: http://localhost:8080/docs
- **ReDoc**: http://localhost:8080/redoc

## API Endpoints

### Authentication Endpoints

#### POST /auth/register
Register a new user with validation.

**Request Body:**

```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "confirmPassword": "SecurePass123!"
}
```

**Response (201 Created):**

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "email": "<EMAIL>",
  "name": "John Doe",
  "createdAt": "2024-01-01T12:00:00Z"
}
```

#### POST /auth/login
Authenticate user and create session with JWT token.

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123!"
}
```

**Response (200 OK):**

```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer"
}
```

> **Note**: The JWT token now includes session information for enhanced security.

#### GET /auth/me
Get current authenticated user information (requires authentication).

**Headers:**

```http
Authorization: Bearer <access_token>
```

**Response (200 OK):**

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "email": "<EMAIL>",
  "name": "John Doe",
  "createdAt": "2024-01-01T12:00:00Z"
}
```

#### POST /auth/logout
Logout user and terminate all active sessions.

**Headers:**

```http
Authorization: Bearer <access_token>
```

**Response (200 OK):**

```json
{
  "message": "User <EMAIL> logged out successfully. 3 sessions terminated.",
  "success": true
}
```

### Session Management Endpoints 🆕

#### GET /sessions/
Get all active sessions for the current user.

**Headers:**

```http
Authorization: Bearer <access_token>
```

**Response (200 OK):**

```json
{
  "sessions": [
    {
      "session_id": "123e4567-e89b-12d3-a456-426614174000",
      "user_id": "550e8400-e29b-41d4-a716-446655440000",
      "expires_at": "2024-01-08T12:00:00Z",
      "device_info": "{\"user_agent\": \"Chrome/91.0\", \"ip\": \"***********\"}",
      "created_at": "2024-01-01T12:00:00Z",
      "last_accessed_at": "2024-01-01T14:30:00Z"
    }
  ],
  "total": 1
}
```

#### DELETE /sessions/terminate
Terminate a specific session.

**Headers:**

```http
Authorization: Bearer <access_token>
```

**Request Body:**

```json
{
  "session_id": "123e4567-e89b-12d3-a456-426614174000"
}
```

**Response (200 OK):**

```json
{
  "message": "Session 123e4567-e89b-12d3-a456-426614174000 terminated successfully",
  "success": true
}
```

#### DELETE /sessions/terminate-all
Terminate all sessions for the current user.

**Headers:**

```http
Authorization: Bearer <access_token>
```

**Response (200 OK):**

```json
{
  "message": "All 3 sessions terminated successfully",
  "success": true
}
```

### Utility Endpoints

#### GET /
Root endpoint with API information.

#### GET /health
Health check endpoint.

## Testing the API

### Using curl

1. **Register a new user:**

```bash
curl -X POST "http://localhost:8080/auth/register" \
     -H "Content-Type: application/json" \
     -d '{
       "firstName": "Test",
       "lastName": "User",
       "email": "<EMAIL>",
       "password": "TestPass123!",
       "confirmPassword": "TestPass123!"
     }'
```

2. **Login (creates session):**

```bash
curl -X POST "http://localhost:8080/auth/login" \
     -H "Content-Type: application/json" \
     -d '{
       "email": "<EMAIL>",
       "password": "TestPass123!"
     }'
```

3. **Get user info (replace TOKEN with actual token):**

```bash
curl -X GET "http://localhost:8080/auth/me" \
     -H "Authorization: Bearer TOKEN"
```

4. **Get active sessions:**

```bash
curl -X GET "http://localhost:8080/sessions/" \
     -H "Authorization: Bearer TOKEN"
```

5. **Terminate a specific session:**

```bash
curl -X DELETE "http://localhost:8080/sessions/terminate" \
     -H "Authorization: Bearer TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "session_id": "your-session-id-here"
     }'
```

6. **Logout (terminates all sessions):**

```bash
curl -X POST "http://localhost:8080/auth/logout" \
     -H "Authorization: Bearer TOKEN"
```

### Using the Interactive API Documentation

Visit <http://localhost:8080/docs> for an interactive Swagger UI where you can:

- Test all endpoints directly in the browser
- View detailed request/response schemas
- Explore the session management features
- See real-time API responses

## Security Features

### Authentication & Authorization

- **Password Hashing**: All passwords are hashed using bcrypt with configurable rounds
- **Hybrid Authentication**: JWT tokens combined with persistent database sessions
- **Password Strength**: Enforced password requirements (8+ chars, uppercase, number, special char)
- **Input Validation**: Comprehensive validation of all inputs using Pydantic schemas

### Session Security 🆕

- **UUID Session IDs**: Cryptographically secure, unpredictable session identifiers
- **Device Fingerprinting**: Capture and store device/browser information for security monitoring
- **Session Expiration**: Configurable session timeouts with automatic cleanup
- **Multi-Device Management**: Users can view and control sessions across all devices
- **Secure Termination**: Individual or bulk session termination capabilities

### Infrastructure Security

- **SQL Injection Protection**: SQLAlchemy ORM prevents SQL injection attacks
- **CORS Configuration**: Restricted to localhost:3000 for frontend development
- **UUID Primary Keys**: All database records use UUIDs instead of sequential IDs
- **Timezone Handling**: Proper UTC timezone handling for session timestamps

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DATABASE_URL` | Database connection string | `sqlite:///./registration.db` |
| `SECRET_KEY` | JWT signing secret key | Generated key |
| `ALGORITHM` | JWT algorithm | `HS256` |
| `ACCESS_TOKEN_EXPIRE_MINUTES` | JWT token expiration time | `30` |
| `SESSION_EXPIRE_HOURS` | Session expiration time | `168` (7 days) |
| `SESSION_CLEANUP_INTERVAL_HOURS` | Automatic cleanup interval | `24` |
| `APP_NAME` | Application name | `Registration Backend` |
| `DEBUG` | Debug mode | `True` |

### Session Configuration

- **`SESSION_EXPIRE_HOURS`**: How long sessions remain valid (default: 7 days)
- **`SESSION_CLEANUP_INTERVAL_HOURS`**: How often to clean expired sessions (default: daily)

Sessions are designed to be longer-lived than JWT tokens to provide persistent authentication across browser sessions while maintaining security.

## Frontend Integration 🆕

This backend now includes comprehensive frontend integration files for React/Next.js applications.

### Integration Package

The `frontend_session_integration/` folder contains:

- **Enhanced API Client**: Real backend integration replacing dummy implementations
- **Session Management UI**: Components for viewing and managing active sessions
- **Enhanced Authentication**: JWT token storage and session validation
- **Updated Types**: TypeScript definitions for session management
- **Enhanced Dashboard**: User interface with session management features

### Quick Frontend Setup

1. **Copy integration files** to your frontend project:

```bash
cp -r frontend_session_integration/src/* frontend/src/
cp frontend_session_integration/package.json frontend/
```

2. **Install dependencies** and start development:

```bash
cd frontend
npm install
npm run dev
```

3. **Access the enhanced application**:
   - Frontend: <http://localhost:3000>
   - Backend API: <http://localhost:8080>
   - API Docs: <http://localhost:8080/docs>

### Frontend Features

- **Session Management Dashboard**: View all active sessions with device information
- **Multi-Device Control**: Terminate sessions on specific devices
- **Security Monitoring**: Track login locations and device types
- **Enhanced Authentication**: Seamless JWT + Session authentication flow

For detailed integration instructions, see `frontend_session_integration/INTEGRATION_GUIDE.md`.

## Production Deployment

For production deployment:

1. **Ensure Python 3.8+** is installed on the production server
2. **Change the SECRET_KEY** to a secure random value
3. **Set DEBUG=False** in environment variables
4. **Configure proper CORS origins** in `app/main.py`
5. **Use a production database** (PostgreSQL, MySQL) instead of SQLite
6. **Set up proper logging** and monitoring
7. **Use a production ASGI server** like Gunicorn with Uvicorn workers
8. **Set up SSL/TLS** for HTTPS in production

## License

This project is open source and available under the MIT License.
